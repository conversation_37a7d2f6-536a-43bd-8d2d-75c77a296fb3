@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 15, 23, 42; /* slate-900 */
  --background-rgb: 255, 255, 255;
  --primary-gradient: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 50%, #6366f1 100%);
  --secondary-gradient: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f8fafc 100%);
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-rgb));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Ensure backdrop-filter support */
@supports (backdrop-filter: blur(10px)) {
  .card,
  .card-feature,
  .form-input,
  .stat-card {
    backdrop-filter: blur(8px);
  }
}

@layer components {
  /* Modern gradient buttons */
  .btn-primary {
    @apply relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white
           px-8 py-4 rounded-2xl font-semibold text-lg
           hover:shadow-2xl hover:scale-105
           transition-all duration-300 overflow-hidden;
  }

  .btn-primary::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500
           opacity-0 hover:opacity-100 transition-opacity duration-300;
  }

  .btn-primary span {
    @apply relative z-10;
  }

  .btn-secondary {
    @apply bg-white text-slate-700 border-2 border-slate-200
           px-8 py-4 rounded-2xl font-semibold text-lg
           hover:border-blue-300 hover:text-blue-600 hover:shadow-lg
           transition-all duration-300;
  }

  /* Navigation links */
  .nav-link {
    @apply text-slate-600 hover:text-blue-600 font-medium
           transition-all duration-200 relative;
  }

  .nav-link::after {
    content: '';
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500
           transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* Modern cards with glassmorphism */
  .card {
    @apply bg-white bg-opacity-80 p-8 rounded-3xl shadow-lg
           border border-gray-200 hover:shadow-2xl hover:scale-105
           transition-all duration-500 group;
    backdrop-filter: blur(4px);
  }

  .card-feature {
    @apply bg-white bg-opacity-90 p-8 rounded-3xl shadow-xl border border-gray-300
           hover:shadow-2xl hover:scale-105 transition-all duration-500 group;
    backdrop-filter: blur(8px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  }

  /* Modern section headings */
  .section-heading {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r
           from-slate-900 via-blue-800 to-purple-800 bg-clip-text text-transparent
           leading-tight;
  }

  .section-subheading {
    @apply text-xl md:text-2xl text-slate-600 font-light leading-relaxed;
  }

  /* Container width for consistent layout */
  .container-width {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Gradient backgrounds */
  .gradient-bg-primary {
    background: var(--primary-gradient);
  }

  .gradient-bg-secondary {
    background: var(--secondary-gradient);
  }

  /* Animated gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600
           bg-clip-text text-transparent;
  }

  /* Form inputs */
  .form-input {
    @apply w-full px-6 py-4 rounded-2xl border-2 border-slate-200
           focus:border-blue-500 focus:ring-4 focus:ring-blue-100
           transition-all duration-300 bg-white bg-opacity-80;
    backdrop-filter: blur(4px);
  }

  .form-label {
    @apply text-slate-600 font-medium;
  }

  /* Form group wrapper for consistent spacing */
  .form-group {
    @apply space-y-2;
  }

  /* Icon containers */
  .icon-container {
    @apply w-16 h-16 rounded-2xl flex items-center justify-center
           bg-gradient-to-br from-blue-50 to-purple-50
           group-hover:from-blue-100 group-hover:to-purple-100
           transition-all duration-300;
  }

  /* Stats cards */
  .stat-card {
    @apply bg-white bg-opacity-90 p-6 rounded-2xl border border-gray-300 text-center
           hover:scale-105 transition-all duration-300;
    backdrop-filter: blur(8px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  }

  /* Feature highlight */
  .feature-highlight {
    @apply border border-blue-200 rounded-2xl p-6 transition-all duration-300;
    background: linear-gradient(to right, rgba(37, 99, 235, 0.1), rgba(147, 51, 234, 0.1));
  }

  .feature-highlight:hover {
    background: linear-gradient(to right, rgba(37, 99, 235, 0.2), rgba(147, 51, 234, 0.2));
  }
}

/* Modern grid pattern with gradient */
.bg-grid-pattern {
  background-image:
    linear-gradient(to right, rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(147, 51, 234, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
}

/* Floating animation */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse glow animation */
@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 40px rgba(147, 51, 234, 0.5); }
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

/* Gradient animation */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Slide in animations */
.slide-in-left {
  transform: translateX(-100px);
  opacity: 0;
  transition: all 0.8s ease-out;
}

.slide-in-right {
  transform: translateX(100px);
  opacity: 0;
  transition: all 0.8s ease-out;
}

.slide-in-up {
  transform: translateY(50px);
  opacity: 0;
  transition: all 0.8s ease-out;
}

.slide-in-left.animate,
.slide-in-right.animate,
.slide-in-up.animate {
  transform: translate(0);
  opacity: 1;
}