'use client';

import { useState } from 'react';
import Logo from './Logo';

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsOpen(false);
    }
  };

  return (
    <nav className="bg-white/80 backdrop-blur-md shadow-lg fixed w-full z-50 border-b border-white/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="relative flex justify-between h-20 items-center">
          {/* Logo */}
          <div className="flex items-center">
            <a
              href="#hero"
              onClick={(e) => {
                e.preventDefault();
                scrollToSection('hero');
              }}
              className="flex items-center hover:scale-105 transition-transform duration-300"
            >
              <Logo />
            </a>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button onClick={() => scrollToSection('features')} className="nav-link">Features</button>
            <button onClick={() => scrollToSection('pricing')} className="nav-link">Pricing</button>
            <button onClick={() => scrollToSection('about')} className="nav-link">About</button>
            <button onClick={() => scrollToSection('contact')} className="nav-link">Contact</button>
            <button className="btn-primary text-sm px-6 py-2">
              <span>Get Started</span>
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 text-slate-600 hover:text-blue-600 transition-all duration-300"
            >
              <i className={`fas ${isOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden bg-white/95 backdrop-blur-md border-t border-white/20">
          <div className="px-4 pt-4 pb-6 space-y-4">
            <button onClick={() => scrollToSection('features')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-blue-600 hover:bg-blue-50/50 transition-all duration-300">Features</button>
            <button onClick={() => scrollToSection('pricing')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-blue-600 hover:bg-blue-50/50 transition-all duration-300">Pricing</button>
            <button onClick={() => scrollToSection('about')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-blue-600 hover:bg-blue-50/50 transition-all duration-300">About</button>
            <button onClick={() => scrollToSection('contact')} className="block w-full text-left px-4 py-3 rounded-xl text-slate-600 hover:text-blue-600 hover:bg-blue-50/50 transition-all duration-300">Contact</button>
            <button className="btn-primary w-full mt-4">
              <span>Get Started</span>
            </button>
          </div>
        </div>
      )}
    </nav>
  );
}