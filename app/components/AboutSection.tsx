export default function AboutSection() {
  const teamMembers = [
    {
      name: "<PERSON><PERSON>",
      role: "CEO & Founder",
      image: "/team/wasim.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON>",
      role: "COO & Co-founder",
      image: "/team/atiq.jpg",
      description: ""
    },
    {
      name: "<PERSON><PERSON><PERSON><PERSON>",
      role: "CTO & Co-founder",
      image: "/team/shoaib.jpg",
      description: ""
    },
    // ... other team members
  ];

  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-purple-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-100 to-blue-100 border border-purple-200/50 mb-6">
            <span className="text-sm font-semibold gradient-text">🚀 About Difinity.ai</span>
          </div>
          <h2 className="section-heading mb-6">
            Redefining <span className="gradient-text">AI Infrastructure</span>
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            We believe organizations should have complete control over their AI workloads while maintaining data privacy and cost transparency.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 mb-20">
          <div className="space-y-8" data-aos="fade-right">
            <div>
              <h3 className="text-3xl font-bold text-slate-800 mb-4">Our Mission</h3>
              <p className="text-xl text-slate-600 leading-relaxed mb-6">
                To democratize AI infrastructure by giving organizations complete control, privacy, and cost transparency over their AI workloads.
              </p>
              <p className="text-slate-600 leading-relaxed">
                We're building the future where AI deployment is infrastructure-agnostic, privacy-first, and cost-intelligent.
                Your data stays where it belongs - with you.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">100%</div>
                <div className="text-slate-600">Data Privacy</div>
              </div>
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">50+</div>
                <div className="text-slate-600">AI Models</div>
              </div>
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">99.9%</div>
                <div className="text-slate-600">Uptime SLA</div>
              </div>
              <div className="stat-card">
                <div className="text-4xl font-bold gradient-text mb-2">60%</div>
                <div className="text-slate-600">Cost Savings</div>
              </div>
            </div>
          </div>

        <div className="grid grid-cols-2 gap-6" data-aos="fade-left">
        {teamMembers.map((member, index) => (
    <div key={index} className="card group">
      <img
        src={member.image}
        alt={member.name}
        className="w-24 h-24 object-cover rounded-full mx-auto mb-4"
      />
      <h4 className="text-lg font-bold text-slate-800 text-center">{member.name}</h4>
      <p className="text-sky-500 font-medium text-sm mb-2 text-center">{member.role}</p>
      <p className="text-slate-600 text-sm text-center">{member.description}</p>
    </div>
  ))}
        </div>
      </div>
    </div>
  );
}