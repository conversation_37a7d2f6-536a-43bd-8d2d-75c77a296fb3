export default function PricingSection() {
  const pricingTiers = [
    {
      name: "Starter",
      price: "Free",
      period: "forever",
      description: "Perfect for small teams getting started with AI",
      features: [
        "Up to 10,000 API calls/month",
        "Basic cost monitoring",
        "Community support",
        "Single deployment option",
        "Standard security"
      ],
      cta: "Start Free",
      popular: false,
      gradient: "from-blue-500 to-cyan-500"
    },
    {
      name: "Professional",
      price: "$99",
      period: "per month",
      description: "Advanced features for growing enterprises",
      features: [
        "Unlimited API calls",
        "Advanced cost intelligence",
        "Priority support",
        "Multi-cloud deployment",
        "Enhanced security & compliance",
        "Custom admin controls",
        "Real-time monitoring",
        "Billing transparency"
      ],
      cta: "Start Trial",
      popular: true,
      gradient: "from-purple-500 to-indigo-500"
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "pricing",
      description: "Complete solution for large organizations",
      features: [
        "Everything in Professional",
        "On-premises deployment",
        "Dedicated support team",
        "Custom integrations",
        "Advanced audit logs",
        "SLA guarantees",
        "White-label options",
        "Training & onboarding"
      ],
      cta: "Contact Sales",
      popular: false,
      gradient: "from-indigo-500 to-purple-500"
    }
  ];

  const comparisons = [
    {
      title: "Traditional Integration",
      points: [
        "Multiple API integrations",
        "Manual compliance checks",
        "Basic monitoring",
        "Fixed routing",
        "Limited support",
      ],
      isNegative: true
    },
    {
      title: "With Difinity.ai",
      points: [
        "Single API integration",
        "Automated compliance",
        "Advanced monitoring",
        "Smart routing",
        "24/7 support",
      ],
      isNegative: false
    }
  ];

  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-white to-blue-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-purple-100 to-blue-100 border border-purple-200/50 mb-6">
            <span className="text-sm font-semibold gradient-text">💰 Transparent Pricing</span>
          </div>
          <h2 className="section-heading mb-6">
            Choose Your <span className="gradient-text">Perfect Plan</span>
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            Start free and scale as you grow. No hidden fees, no surprises - just transparent pricing that grows with your business.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {pricingTiers.map((tier, index) => (
            <div
              key={index}
              className={`relative ${tier.popular ? 'lg:scale-105 lg:-mt-4' : ''}`}
              data-aos="fade-up"
              data-aos-delay={index * 150}
            >
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-2 rounded-full text-sm font-semibold">
                    Most Popular
                  </div>
                </div>
              )}

              <div className={`card-feature h-full ${tier.popular ? 'border-purple-200 shadow-2xl' : ''}`}>
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-slate-800 mb-2">{tier.name}</h3>
                  <p className="text-slate-600 mb-6">{tier.description}</p>

                  <div className="mb-6">
                    <span className={`text-5xl font-bold bg-gradient-to-r ${tier.gradient} bg-clip-text text-transparent`}>
                      {tier.price}
                    </span>
                    <span className="text-slate-500 ml-2">{tier.period}</span>
                  </div>

                  <button className={`w-full ${tier.popular ? 'btn-primary' : 'btn-secondary'}`}>
                    <span>{tier.cta}</span>
                  </button>
                </div>

                <div className="space-y-4">
                  {tier.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center">
                      <i className="fas fa-check text-green-500 mr-3"></i>
                      <span className="text-slate-600">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Value Proposition */}
        <div className="text-center" data-aos="fade-up">
          <div className="bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl p-12 border border-blue-200/50">
            <h3 className="text-3xl font-bold mb-6 gradient-text">Why Choose Difinity.ai?</h3>

            <div className="grid md:grid-cols-2 gap-8 mb-8">
              <div className="text-left">
                <h4 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                  <i className="fas fa-times text-red-500 mr-3"></i>
                  Traditional Approach
                </h4>
                <ul className="space-y-3 text-slate-600">
                  <li>• Data leaves your infrastructure</li>
                  <li>• Hidden costs and surprise bills</li>
                  <li>• Limited deployment options</li>
                  <li>• Complex vendor management</li>
                  <li>• No cost transparency</li>
                </ul>
              </div>

              <div className="text-left">
                <h4 className="text-xl font-bold text-slate-800 mb-4 flex items-center">
                  <i className="fas fa-check text-green-500 mr-3"></i>
                  Difinity.ai Advantage
                </h4>
                <ul className="space-y-3 text-slate-600">
                  <li>• Complete data sovereignty</li>
                  <li>• Transparent cost monitoring</li>
                  <li>• Deploy anywhere you need</li>
                  <li>• Single platform management</li>
                  <li>• Real-time cost intelligence</li>
                </ul>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                <span>Start Free Trial</span>
              </button>
              <button className="btn-secondary">
                <span>Schedule Demo</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}