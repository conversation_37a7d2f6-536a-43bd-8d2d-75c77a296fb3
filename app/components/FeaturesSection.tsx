export default function FeaturesSection() {
  const mainFeatures = [
    {
      title: "Privacy-First Architecture",
      description: "Your data never leaves your infrastructure. Complete data sovereignty and privacy control.",
      icon: "shield-alt",
      gradient: "from-green-500 to-emerald-600"
    },
    {
      title: "Admin Control Center",
      description: "Centralized control over all AI workloads, user permissions, and resource allocation.",
      icon: "cogs",
      gradient: "from-blue-500 to-cyan-600"
    },
    {
      title: "Cost Intelligence",
      description: "Real-time cost monitoring, usage analytics, and automated optimization recommendations.",
      icon: "chart-line",
      gradient: "from-purple-500 to-violet-600"
    },
    {
      title: "Multi-Cloud Deployment",
      description: "Deploy anywhere - on-premises, hybrid, AWS, Azure, GCP. Infrastructure agnostic.",
      icon: "cloud",
      gradient: "from-indigo-500 to-blue-600"
    },
    {
      title: "Usage Monitoring",
      description: "Track every API call, model usage, and performance metrics in real-time.",
      icon: "eye",
      gradient: "from-orange-500 to-red-600"
    },
    {
      title: "Billing Transparency",
      description: "Detailed billing breakdowns, cost allocation, and budget alerts for complete financial control.",
      icon: "dollar-sign",
      gradient: "from-yellow-500 to-orange-600"
    }
  ];

  const deploymentOptions = [
    {
      title: "On-Premises",
      description: "Complete control with on-site deployment",
      icon: "server",
      features: ["Air-gapped security", "Custom hardware", "Zero cloud dependency"]
    },
    {
      title: "Hybrid Cloud",
      description: "Best of both worlds - flexibility and control",
      icon: "cloud-upload-alt",
      features: ["Seamless scaling", "Data locality", "Cost optimization"]
    },
    {
      title: "Multi-Cloud",
      description: "Deploy across multiple cloud providers",
      icon: "globe",
      features: ["Vendor independence", "Global reach", "Disaster recovery"]
    }
  ];

  return (
    <div className="py-32 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/30"></div>
      <div className="absolute inset-0 bg-grid-pattern opacity-20"></div>

      <div className="container-width relative z-10">
        {/* Section Header */}
        <div className="text-center mb-20" data-aos="fade-up">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-blue-100 to-purple-100 border border-blue-200/50 mb-6">
            <span className="text-sm font-semibold gradient-text">🔒 Enterprise-Grade Features</span>
          </div>
          <h2 className="section-heading mb-6">
            Privacy, Control & <span className="gradient-text">Cost Intelligence</span>
          </h2>
          <p className="section-subheading max-w-3xl mx-auto">
            Everything you need to deploy, monitor, and control AI workloads while maintaining complete data sovereignty and cost transparency.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
          {mainFeatures.map((feature, index) => (
            <div
              key={index}
              className="card-feature group"
              data-aos="fade-up"
              data-aos-delay={index * 100}
            >
              <div className="icon-container mb-6">
                <i className={`fas fa-${feature.icon} text-2xl bg-gradient-to-r ${feature.gradient} bg-clip-text text-transparent`}></i>
              </div>
              <h3 className="text-xl font-bold mb-4 text-slate-800 group-hover:text-slate-900 transition-colors">
                {feature.title}
              </h3>
              <p className="text-slate-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Deployment Options */}
        <div className="mb-24">
          <div className="text-center mb-16" data-aos="fade-up">
            <h3 className="text-3xl md:text-4xl font-bold mb-4 gradient-text">Deploy Anywhere</h3>
            <p className="text-xl text-slate-600">Infrastructure agnostic - run it where you need it</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {deploymentOptions.map((option, index) => (
              <div
                key={index}
                className="feature-highlight group text-center"
                data-aos="fade-up"
                data-aos-delay={index * 150}
              >
                <div className="icon-container mx-auto mb-6">
                  <i className={`fas fa-${option.icon} text-2xl text-blue-600`}></i>
                </div>
                <h4 className="text-xl font-bold mb-3 text-slate-800">{option.title}</h4>
                <p className="text-slate-600 mb-6">{option.description}</p>
                <ul className="space-y-2">
                  {option.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center justify-center text-sm text-slate-600">
                      <i className="fas fa-check text-green-500 mr-2"></i>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center" data-aos="fade-up">
          <div className="rounded-3xl p-12 border border-blue-200" style={{background: 'linear-gradient(to right, rgba(37, 99, 235, 0.1), rgba(147, 51, 234, 0.1))'}}>
            <h3 className="text-3xl font-bold mb-4 gradient-text">Ready to Get Started?</h3>
            <p className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto">
              Experience the power of privacy-first AI infrastructure with complete cost transparency and control.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-primary">
                <span>Start Free Trial</span>
              </button>
              <button className="btn-secondary">
                <span>Schedule Demo</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}